use enigo::{<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>};
use rand::Rng;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;

/// Auto Clicker Application
///
/// This application performs automated mouse clicks at randomized intervals
/// to simulate human-like behavior. The click timing is randomized between
/// 100ms and 700ms, and clicks are performed at the current cursor position.
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Create a flag to handle graceful shutdown
    let running = Arc::new(AtomicBool::new(true));
    let r = running.clone();

    // Set up Ctrl+C handler for graceful shutdown
    ctrlc::set_handler(move || {
        println!("\n🛑 Shutdown signal received. Stopping auto clicker...");
        r.store(false, Ordering::SeqCst);
    })?;

    // Initialize the input controller
    let mut enigo = Enigo::new(&enigo::Settings::default())
        .map_err(|e| format!("Failed to initialize input controller: {}", e))?;

    // Display startup message
    println!("🖱️  Auto Clicker Started!");
    println!("📍 Clicking at current cursor position");
    println!("⏱️  Random intervals: 100ms - 700ms");
    println!("🛑 Press Ctrl+C to stop");
    println!();
    println!("⏰ Starting in 3 seconds...");
    println!("💡 Use this time to:");
    println!("   • Position your mouse cursor where you want clicks");
    println!("   • Focus on the target window/application");
    println!("   • Get ready!");

    // Countdown with cancellation check
    for i in (1..=3).rev() {
        if !running.load(Ordering::SeqCst) {
            println!("🛑 Cancelled during startup");
            return Ok(());
        }
        println!("⏰ Starting in {}...", i);
        sleep(Duration::from_secs(1)).await;
    }

    // Check one more time before starting
    if !running.load(Ordering::SeqCst) {
        println!("🛑 Cancelled during startup");
        return Ok(());
    }

    println!("🚀 Starting auto clicker now!");
    println!("---");

    let mut rng = rand::thread_rng();
    let mut click_count = 0u64;

    // Main clicking loop
    while running.load(Ordering::SeqCst) {
        // Generate random interval between 100ms and 700ms
        let interval_ms = rng.gen_range(100..=700);
        let interval = Duration::from_millis(interval_ms);

        // Optional debug output showing next click interval
        println!("⏳ Next click in {}ms...", interval_ms);

        // Wait for the randomized interval
        sleep(interval).await;

        // Check if we should still be running after the sleep
        if !running.load(Ordering::SeqCst) {
            break;
        }

        // Perform the click at current cursor position
        match enigo.button(Button::Left, Direction::Click) {
            Ok(_) => {
                click_count += 1;
                println!("🖱️  Click #{} performed", click_count);
            }
            Err(e) => {
                eprintln!("❌ Error performing click: {}", e);
                // Continue running even if a single click fails
            }
        }
    }

    // Display shutdown message
    println!("---");
    println!("✅ Auto Clicker stopped gracefully");
    println!("📊 Total clicks performed: {}", click_count);
    println!("👋 Goodbye!");

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_random_interval_generation() {
        let mut rng = rand::thread_rng();

        // Test that random intervals are within the expected range
        for _ in 0..100 {
            let interval_ms = rng.gen_range(100..=700);
            assert!(interval_ms >= 100);
            assert!(interval_ms <= 700);
        }
    }

    #[test]
    fn test_atomic_bool_operations() {
        let running = Arc::new(AtomicBool::new(true));
        assert!(running.load(Ordering::SeqCst));

        running.store(false, Ordering::SeqCst);
        assert!(!running.load(Ordering::SeqCst));
    }

    #[test]
    fn test_countdown_range() {
        // Test that countdown values are in expected range
        let countdown_values: Vec<u64> = (1..=3).rev().collect();
        assert_eq!(countdown_values, vec![3, 2, 1]);

        // Test that we have exactly 3 seconds of countdown
        assert_eq!(countdown_values.len(), 3);
    }
}
