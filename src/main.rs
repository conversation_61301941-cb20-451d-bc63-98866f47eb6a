use enigo::{<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>};
use rand::Rng;
use std::env;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::time::sleep;

/// Auto Clicker Application
///
/// This application performs automated mouse clicks at randomized intervals
/// to simulate human-like behavior. The click timing is randomized between
/// 100ms and 700ms, and clicks are performed at the current cursor position.
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Check for command line arguments
    let args: Vec<String> = env::args().collect();
    let check_mode = args.len() > 1 && args[1] == "check_function()";

    // Create a flag to handle graceful shutdown
    let running = Arc::new(AtomicBool::new(true));
    let r = running.clone();

    // Set up Ctrl+C handler for graceful shutdown
    ctrlc::set_handler(move || {
        println!("\n🛑 Shutdown signal received. Stopping auto clicker...");
        r.store(false, Ordering::SeqCst);
    })?;

    // Initialize the input controller
    let mut enigo = Enigo::new(&enigo::Settings::default())
        .map_err(|e| format!("Failed to initialize input controller: {}", e))?;

    // Display startup message
    if check_mode {
        println!("🔍 Auto Clicker - CHECK FUNCTION MODE");
        println!("📊 This mode shows detailed timing information");
        println!("📍 Clicking at current cursor position");
        println!("⏱️  Random intervals: 100ms - 700ms");
        println!("� Detailed timing analysis will be displayed");
        println!("🛑 Press Ctrl+C to stop");
    } else {
        println!("�🖱️  Auto Clicker Started!");
        println!("📍 Clicking at current cursor position");
        println!("⏱️  Random intervals: 100ms - 700ms");
        println!("🛑 Press Ctrl+C to stop");
        println!("💡 Tip: Use 'cargo run check_function()' for detailed timing info");
    }
    println!();
    println!("⏰ Starting in 3 seconds...");
    println!("💡 Use this time to:");
    println!("   • Position your mouse cursor where you want clicks");
    println!("   • Focus on the target window/application");
    println!("   • Get ready!");

    // Countdown with cancellation check
    for i in (1..=3).rev() {
        if !running.load(Ordering::SeqCst) {
            println!("🛑 Cancelled during startup");
            return Ok(());
        }
        println!("⏰ Starting in {}...", i);
        sleep(Duration::from_secs(1)).await;
    }

    // Check one more time before starting
    if !running.load(Ordering::SeqCst) {
        println!("🛑 Cancelled during startup");
        return Ok(());
    }

    println!("🚀 Starting auto clicker now!");
    println!("---");

    let mut rng = rand::thread_rng();
    let mut click_count = 0u64;
    let mut last_click_time: Option<Instant> = None;
    let mut total_wait_time = Duration::ZERO;
    let mut total_actual_intervals: Vec<u64> = Vec::new();

    // Main clicking loop
    while running.load(Ordering::SeqCst) {
        // Generate random interval between 100ms and 700ms
        let interval_ms = rng.gen_range(100..=700);
        let interval = Duration::from_millis(interval_ms);

        if check_mode {
            println!("🔍 [CHECK] Generated interval: {}ms", interval_ms);
            if let Some(last_time) = last_click_time {
                let actual_elapsed = last_time.elapsed();
                let actual_ms = actual_elapsed.as_millis() as u64;
                total_actual_intervals.push(actual_ms);
                println!("📊 [CHECK] Actual time since last click: {}ms", actual_ms);
            }
            println!("⏳ [CHECK] Waiting {}ms before next click...", interval_ms);
        } else {
            println!("⏳ Next click in {}ms...", interval_ms);
        }

        let wait_start = Instant::now();

        // Wait for the randomized interval
        sleep(interval).await;

        let actual_wait = wait_start.elapsed();
        total_wait_time += actual_wait;

        // Check if we should still be running after the sleep
        if !running.load(Ordering::SeqCst) {
            break;
        }

        let click_start = Instant::now();

        // Perform the click at current cursor position
        match enigo.button(Button::Left, Direction::Click) {
            Ok(_) => {
                click_count += 1;
                let click_duration = click_start.elapsed();

                if check_mode {
                    println!("✅ [CHECK] Click #{} performed in {}μs", click_count, click_duration.as_micros());
                    println!("⏱️  [CHECK] Actual wait time: {}ms (expected: {}ms)",
                        actual_wait.as_millis(), interval_ms);
                    if actual_wait.as_millis() as u64 != interval_ms {
                        let drift = actual_wait.as_millis() as i64 - interval_ms as i64;
                        println!("⚠️  [CHECK] Timing drift: {}ms", drift);
                    }
                    println!("---");
                } else {
                    println!("🖱️  Click #{} performed", click_count);
                }

                last_click_time = Some(Instant::now());
            }
            Err(e) => {
                eprintln!("❌ Error performing click: {}", e);
                // Continue running even if a single click fails
            }
        }
    }

    // Display shutdown message
    println!("---");
    println!("✅ Auto Clicker stopped gracefully");
    println!("📊 Total clicks performed: {}", click_count);

    if check_mode && !total_actual_intervals.is_empty() {
        println!();
        println!("📈 DETAILED TIMING STATISTICS:");
        println!("🔍 Total wait time: {}ms", total_wait_time.as_millis());

        let avg_interval = total_actual_intervals.iter().sum::<u64>() as f64 / total_actual_intervals.len() as f64;
        println!("📊 Average actual interval: {:.2}ms", avg_interval);

        let min_interval = total_actual_intervals.iter().min().unwrap_or(&0);
        let max_interval = total_actual_intervals.iter().max().unwrap_or(&0);
        println!("⏱️  Min interval: {}ms, Max interval: {}ms", min_interval, max_interval);

        if click_count > 0 {
            let avg_wait_per_click = total_wait_time.as_millis() as f64 / click_count as f64;
            println!("⏳ Average wait per click: {:.2}ms", avg_wait_per_click);
        }

        println!("🎯 Expected range: 100ms - 700ms");
    }

    println!("👋 Goodbye!");

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_random_interval_generation() {
        let mut rng = rand::thread_rng();

        // Test that random intervals are within the expected range
        for _ in 0..100 {
            let interval_ms = rng.gen_range(100..=700);
            assert!(interval_ms >= 100);
            assert!(interval_ms <= 700);
        }
    }

    #[test]
    fn test_atomic_bool_operations() {
        let running = Arc::new(AtomicBool::new(true));
        assert!(running.load(Ordering::SeqCst));

        running.store(false, Ordering::SeqCst);
        assert!(!running.load(Ordering::SeqCst));
    }

    #[test]
    fn test_countdown_range() {
        // Test that countdown values are in expected range
        let countdown_values: Vec<u64> = (1..=3).rev().collect();
        assert_eq!(countdown_values, vec![3, 2, 1]);

        // Test that we have exactly 3 seconds of countdown
        assert_eq!(countdown_values.len(), 3);
    }

    #[test]
    fn test_check_mode_detection() {
        // Test that check_function() argument is detected correctly
        let args = vec!["program".to_string(), "check_function()".to_string()];
        let check_mode = args.len() > 1 && args[1] == "check_function()";
        assert!(check_mode);

        // Test normal mode
        let args = vec!["program".to_string()];
        let check_mode = args.len() > 1 && args[1] == "check_function()";
        assert!(!check_mode);

        // Test other arguments
        let args = vec!["program".to_string(), "other".to_string()];
        let check_mode = args.len() > 1 && args[1] == "check_function()";
        assert!(!check_mode);
    }
}
