use enigo::{<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Direction, Coordinate};
use global_hotkey::{GlobalHotKeyManager, hotkey::{HotKey, Modifiers, Code}};
use rand::{Rng, distributions::WeightedIndex, prelude::*};
use std::env;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::time::sleep;

/// Human-like behavior patterns
struct HumanBehavior {
    rng: ThreadRng,
    last_intervals: Vec<u64>,
    click_count: u64,
    session_start: Instant,
    fatigue_factor: f64,
}

impl HumanBehavior {
    fn new() -> Self {
        Self {
            rng: rand::thread_rng(),
            last_intervals: Vec::new(),
            click_count: 0,
            session_start: Instant::now(),
            fatigue_factor: 1.0,
        }
    }

    /// Generate human-like interval with multiple factors
    fn generate_interval(&mut self) -> u64 {
        // Base interval range: 100-700ms
        let base_min = 100;
        let base_max = 700;

        // 1. Weighted distribution favoring mid-range intervals (more human-like)
        let weights = [1, 3, 5, 8, 10, 8, 5, 3, 1]; // Bell curve-ish
        let ranges = [
            (100, 150), (150, 200), (200, 250), (250, 350), (350, 450),
            (450, 550), (550, 600), (600, 650), (650, 700)
        ];

        let dist = WeightedIndex::new(weights).unwrap();
        let selected_range = ranges[dist.sample(&mut self.rng)];
        let mut interval = self.rng.gen_range(selected_range.0..=selected_range.1);

        // 2. Avoid repetitive patterns (humans don't click at exact same intervals)
        if self.last_intervals.len() >= 3 {
            let recent_avg = self.last_intervals.iter().rev().take(3).sum::<u64>() / 3;
            let diff = (interval as i64 - recent_avg as i64).abs();

            // If too similar to recent pattern, add variation
            if diff < 50 {
                let variation = self.rng.gen_range(-100..=100);
                interval = ((interval as i64 + variation).max(base_min as i64).min(base_max as i64)) as u64;
            }
        }

        // 3. Fatigue simulation (slower clicks over time)
        let session_duration = self.session_start.elapsed().as_secs() as f64;
        if session_duration > 30.0 { // After 30 seconds, start getting "tired"
            self.fatigue_factor = 1.0 + (session_duration - 30.0) * 0.01; // 1% slower per second
            interval = (interval as f64 * self.fatigue_factor) as u64;
        }

        // 4. Occasional "thinking pauses" (humans sometimes hesitate)
        if self.rng.gen_ratio(1, 20) { // 5% chance
            interval += self.rng.gen_range(500..=2000); // Add 0.5-2 second pause
        }

        // 5. Burst patterns (humans sometimes click faster in bursts)
        if self.click_count > 0 && self.rng.gen_ratio(1, 15) { // 6.7% chance
            interval = (interval as f64 * 0.6) as u64; // 40% faster
        }

        // Keep within bounds
        interval = interval.max(base_min).min(base_max * 3); // Allow up to 3x max for pauses

        // Store for pattern analysis
        self.last_intervals.push(interval);
        if self.last_intervals.len() > 10 {
            self.last_intervals.remove(0);
        }

        self.click_count += 1;
        interval
    }

    /// Generate slight mouse movement (humans rarely click at exact same spot)
    fn generate_mouse_jitter(&mut self) -> (i32, i32) {
        // Small random movement within 3-pixel radius
        let angle = self.rng.gen_range(0.0..std::f64::consts::TAU);
        let radius = self.rng.gen_range(0.0..3.0);

        let dx = (radius * angle.cos()) as i32;
        let dy = (radius * angle.sin()) as i32;

        (dx, dy)
    }

    /// Simulate human click timing variations
    fn generate_click_duration(&mut self) -> Duration {
        // Humans don't have perfect click timing
        // Mouse down and up aren't instantaneous
        Duration::from_micros(self.rng.gen_range(10_000..=50_000)) // 10-50ms
    }
}

/// Auto Clicker Application
///
/// This application performs automated mouse clicks at randomized intervals
/// to simulate human-like behavior. The click timing is randomized between
/// 100ms and 700ms, and clicks are performed at the current cursor position.
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Check for command line arguments
    let args: Vec<String> = env::args().collect();
    let check_mode = args.len() > 1 && args[1] == "check_function()";

    // Create a flag to handle graceful shutdown
    let running = Arc::new(AtomicBool::new(true));
    let r = running.clone();

    // Set up Ctrl+C handler for graceful shutdown (when terminal is in focus)
    ctrlc::set_handler(move || {
        println!("\n🛑 Shutdown signal received. Stopping auto clicker...");
        r.store(false, Ordering::SeqCst);
    })?;

    // Set up global hotkey manager for stopping when terminal is not in focus
    let manager = GlobalHotKeyManager::new().map_err(|e| format!("Failed to create global hotkey manager: {}", e))?;

    // Register Ctrl+Shift+Q as global stop hotkey
    let hotkey = HotKey::new(Some(Modifiers::CONTROL | Modifiers::SHIFT), Code::KeyQ);
    manager.register(hotkey).map_err(|e| format!("Failed to register global hotkey: {}", e))?;

    let running_hotkey = running.clone();

    // Start hotkey event listener in a separate task
    let hotkey_task = tokio::spawn(async move {
        use global_hotkey::GlobalHotKeyEvent;

        loop {
            if let Ok(event) = GlobalHotKeyEvent::receiver().try_recv() {
                if event.state == global_hotkey::HotKeyState::Pressed {
                    println!("\n🛑 Global hotkey pressed (Ctrl+Shift+Q). Stopping auto clicker...");
                    running_hotkey.store(false, Ordering::SeqCst);
                    break;
                }
            }
            tokio::time::sleep(Duration::from_millis(10)).await;
        }
    });

    // Initialize the input controller
    let mut enigo = Enigo::new(&enigo::Settings::default())
        .map_err(|e| format!("Failed to initialize input controller: {}", e))?;

    // Display startup message
    if check_mode {
        println!("🔍 Auto Clicker - CHECK FUNCTION MODE");
        println!("📊 This mode shows detailed timing information");
        println!("📍 Clicking with human-like behavior patterns");
        println!("⏱️  Adaptive intervals: 100ms - 700ms (+ human factors)");
        println!("🧠 Human simulation: fatigue, patterns, jitter, pauses");
        println!("� Detailed timing analysis will be displayed");
        println!("🛑 Stop: Ctrl+C (terminal focus) or Ctrl+Shift+Q (global)");
    } else {
        println!("�🖱️  Auto Clicker Started!");
        println!("📍 Clicking with human-like behavior patterns");
        println!("⏱️  Adaptive intervals: 100ms - 700ms (+ human factors)");
        println!("🧠 Features: fatigue simulation, pattern avoidance, micro-jitter");
        println!("🛑 Stop: Ctrl+C (terminal focus) or Ctrl+Shift+Q (global)");
        println!("💡 Tip: Use 'cargo run check_function()' for detailed timing info");
    }
    println!();
    println!("⏰ Starting in 3 seconds...");
    println!("💡 Use this time to:");
    println!("   • Position your mouse cursor where you want clicks");
    println!("   • Focus on the target window/application");
    println!("   • Get ready!");

    // Countdown with cancellation check
    for i in (1..=3).rev() {
        if !running.load(Ordering::SeqCst) {
            println!("🛑 Cancelled during startup");
            return Ok(());
        }
        println!("⏰ Starting in {}...", i);
        sleep(Duration::from_secs(1)).await;
    }

    // Check one more time before starting
    if !running.load(Ordering::SeqCst) {
        println!("🛑 Cancelled during startup");
        return Ok(());
    }

    println!("🚀 Starting auto clicker now!");
    println!("---");

    let mut human_behavior = HumanBehavior::new();
    let mut click_count = 0u64;
    let mut last_click_time: Option<Instant> = None;
    let mut total_wait_time = Duration::ZERO;
    let mut total_actual_intervals: Vec<u64> = Vec::new();
    let mut initial_cursor_pos: Option<(i32, i32)> = None;

    // Main clicking loop
    while running.load(Ordering::SeqCst) {
        // Generate human-like interval
        let interval_ms = human_behavior.generate_interval();
        let interval = Duration::from_millis(interval_ms);

        if check_mode {
            println!("🔍 [CHECK] Generated interval: {}ms (human-like)", interval_ms);
            if let Some(last_time) = last_click_time {
                let actual_elapsed = last_time.elapsed();
                let actual_ms = actual_elapsed.as_millis() as u64;
                total_actual_intervals.push(actual_ms);
                println!("📊 [CHECK] Actual time since last click: {}ms", actual_ms);
            }
            println!("🧠 [CHECK] Fatigue factor: {:.2}x", human_behavior.fatigue_factor);
            println!("📈 [CHECK] Pattern history: {:?}", human_behavior.last_intervals.iter().rev().take(3).collect::<Vec<_>>());
            println!("⏳ [CHECK] Waiting {}ms before next click...", interval_ms);
        } else {
            println!("⏳ Next click in {}ms...", interval_ms);
        }

        let wait_start = Instant::now();

        // Wait for the randomized interval
        sleep(interval).await;

        let actual_wait = wait_start.elapsed();
        total_wait_time += actual_wait;

        // Check if we should still be running after the sleep
        if !running.load(Ordering::SeqCst) {
            break;
        }

        // Get current cursor position for jitter calculation
        let current_pos = match enigo.location() {
            Ok(pos) => (pos.0, pos.1),
            Err(_) => initial_cursor_pos.unwrap_or_default()
        };

        // Store initial position for reference
        if initial_cursor_pos.is_none() {
            initial_cursor_pos = Some(current_pos);
        }

        // Generate human-like mouse jitter
        let (jitter_x, jitter_y) = human_behavior.generate_mouse_jitter();
        let target_pos = (current_pos.0 + jitter_x, current_pos.1 + jitter_y);

        let click_start = Instant::now();

        // Apply micro-jitter by moving mouse slightly
        if jitter_x != 0 || jitter_y != 0 {
            let _ = enigo.move_mouse(target_pos.0, target_pos.1, Coordinate::Abs);
            // Small delay to simulate human mouse movement
            tokio::time::sleep(Duration::from_micros(human_behavior.rng.gen_range(1000..=5000))).await;
        }

        // Perform human-like click with variable timing
        let click_duration = human_behavior.generate_click_duration();

        // Mouse down
        match enigo.button(Button::Left, Direction::Press) {
            Ok(_) => {
                // Hold for human-like duration
                tokio::time::sleep(click_duration).await;

                // Mouse up
                match enigo.button(Button::Left, Direction::Release) {
                    Ok(_) => {
                        click_count += 1;
                        let total_click_time = click_start.elapsed();

                        if check_mode {
                            println!("✅ [CHECK] Click #{} performed in {}μs", click_count, total_click_time.as_micros());
                            println!("🎯 [CHECK] Jitter applied: ({}, {}) pixels", jitter_x, jitter_y);
                            println!("⏱️  [CHECK] Click hold time: {}μs", click_duration.as_micros());
                            println!("⏱️  [CHECK] Actual wait time: {}ms (expected: {}ms)",
                                actual_wait.as_millis(), interval_ms);
                            if actual_wait.as_millis() as u64 != interval_ms {
                                let drift = actual_wait.as_millis() as i64 - interval_ms as i64;
                                println!("⚠️  [CHECK] Timing drift: {}ms", drift);
                            }
                            println!("---");
                        } else {
                            println!("🖱️  Click #{} performed", click_count);
                        }

                        last_click_time = Some(Instant::now());
                    }
                    Err(e) => {
                        eprintln!("❌ Error releasing mouse button: {}", e);
                    }
                }
            }
            Err(e) => {
                eprintln!("❌ Error pressing mouse button: {}", e);
            }
        }
    }

    // Display shutdown message
    println!("---");
    println!("✅ Auto Clicker stopped gracefully");
    println!("📊 Total clicks performed: {}", click_count);

    if check_mode && !total_actual_intervals.is_empty() {
        println!();
        println!("📈 DETAILED TIMING STATISTICS:");
        println!("🔍 Total wait time: {}ms", total_wait_time.as_millis());

        let avg_interval = total_actual_intervals.iter().sum::<u64>() as f64 / total_actual_intervals.len() as f64;
        println!("📊 Average actual interval: {:.2}ms", avg_interval);

        let min_interval = total_actual_intervals.iter().min().unwrap_or(&0);
        let max_interval = total_actual_intervals.iter().max().unwrap_or(&0);
        println!("⏱️  Min interval: {}ms, Max interval: {}ms", min_interval, max_interval);

        if click_count > 0 {
            let avg_wait_per_click = total_wait_time.as_millis() as f64 / click_count as f64;
            println!("⏳ Average wait per click: {:.2}ms", avg_wait_per_click);
        }

        println!("🎯 Expected range: 100ms - 700ms");
    }

    println!("👋 Goodbye!");

    // Clean up the hotkey task
    hotkey_task.abort();

    // Unregister the global hotkey
    let _ = manager.unregister(hotkey);

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_random_interval_generation() {
        let mut rng = rand::thread_rng();

        // Test that random intervals are within the expected range
        for _ in 0..100 {
            let interval_ms = rng.gen_range(100..=700);
            assert!(interval_ms >= 100);
            assert!(interval_ms <= 700);
        }
    }

    #[test]
    fn test_atomic_bool_operations() {
        let running = Arc::new(AtomicBool::new(true));
        assert!(running.load(Ordering::SeqCst));

        running.store(false, Ordering::SeqCst);
        assert!(!running.load(Ordering::SeqCst));
    }

    #[test]
    fn test_countdown_range() {
        // Test that countdown values are in expected range
        let countdown_values: Vec<u64> = (1..=3).rev().collect();
        assert_eq!(countdown_values, vec![3, 2, 1]);

        // Test that we have exactly 3 seconds of countdown
        assert_eq!(countdown_values.len(), 3);
    }

    #[test]
    fn test_check_mode_detection() {
        // Test that check_function() argument is detected correctly
        let args = vec!["program".to_string(), "check_function()".to_string()];
        let check_mode = args.len() > 1 && args[1] == "check_function()";
        assert!(check_mode);

        // Test normal mode
        let args = vec!["program".to_string()];
        let check_mode = args.len() > 1 && args[1] == "check_function()";
        assert!(!check_mode);

        // Test other arguments
        let args = vec!["program".to_string(), "other".to_string()];
        let check_mode = args.len() > 1 && args[1] == "check_function()";
        assert!(!check_mode);
    }

    #[test]
    fn test_global_hotkey_creation() {
        use global_hotkey::hotkey::{HotKey, Modifiers, Code};

        // Test that we can create the global hotkey (same as used in main)
        let hotkey = HotKey::new(Some(Modifiers::CONTROL | Modifiers::SHIFT), Code::KeyQ);

        // Just verify we can create it without panicking
        assert_eq!(hotkey.key, Code::KeyQ);

        // Test that we can create hotkey without modifiers
        let simple_hotkey = HotKey::new(None, Code::KeyQ);
        assert_eq!(simple_hotkey.key, Code::KeyQ);
    }

    #[test]
    fn test_human_behavior_interval_generation() {
        let mut behavior = HumanBehavior::new();

        // Test that intervals are within reasonable bounds
        for _ in 0..50 {
            let interval = behavior.generate_interval();
            assert!(interval >= 100, "Interval {} too small", interval);
            assert!(interval <= 2100, "Interval {} too large", interval); // 3x max for pauses
        }

        // Test that we get some variation
        let intervals: Vec<u64> = (0..10).map(|_| behavior.generate_interval()).collect();
        let unique_intervals: std::collections::HashSet<u64> = intervals.into_iter().collect();
        assert!(unique_intervals.len() > 1, "Should generate varied intervals");
    }

    #[test]
    fn test_human_behavior_jitter() {
        let mut behavior = HumanBehavior::new();

        // Test jitter generation
        for _ in 0..20 {
            let (dx, dy) = behavior.generate_mouse_jitter();

            // Should be within 3-pixel radius
            let distance = ((dx * dx + dy * dy) as f64).sqrt();
            assert!(distance <= 3.0, "Jitter distance {} exceeds 3 pixels", distance);
        }
    }

    #[test]
    fn test_human_behavior_click_duration() {
        let mut behavior = HumanBehavior::new();

        // Test click duration generation
        for _ in 0..20 {
            let duration = behavior.generate_click_duration();
            assert!(duration >= Duration::from_micros(10_000), "Click duration too short");
            assert!(duration <= Duration::from_micros(50_000), "Click duration too long");
        }
    }
}
