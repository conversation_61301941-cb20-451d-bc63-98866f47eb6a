# Auto Clicker (ac-rs)

A cross-platform auto clicker application written in Rust that performs mouse clicks at randomized intervals to simulate human-like behavior.

## Features

- 🖱️ **Automated Clicking**: Performs left mouse button clicks with advanced human-like behavior
- 🧠 **Human-Like Behavior**: Advanced simulation of human clicking patterns
  - **Weighted Intervals**: Bell-curve distribution favoring mid-range timings (250-450ms)
  - **Pattern Avoidance**: Prevents repetitive clicking patterns that look robotic
  - **Fatigue Simulation**: Gradually slower clicks over time (after 30 seconds)
  - **Thinking Pauses**: Occasional 0.5-2 second hesitations (5% chance)
  - **Burst Patterns**: Sometimes clicks faster in short bursts (6.7% chance)
  - **Micro-Jitter**: Tiny mouse movements (±3 pixels) to simulate hand tremor
  - **Variable Click Duration**: Human-like mouse press/release timing (10-50ms)
- ⏱️ **Adaptive Intervals**: Base range 100ms-700ms with intelligent human factors
- 🛑 **Graceful Shutdown**: Clean exit with Ctrl+C or global hotkey Ctrl+Shift+Q
- 📊 **Click Counter**: Tracks and displays total number of clicks performed
- 🔧 **Cross-Platform**: Works on Linux, Windows, and macOS
- 📝 **Debug Output**: Detailed timing analysis and human behavior insights

## Requirements

### Nix Environment (Recommended)
- Nix package manager installed
- All dependencies are automatically provided via `shell.nix`

### Manual Installation
- Rust 1.70+ with Cargo
- System dependencies:
  - **Linux**: X11 development libraries (`libX11-dev`, `libXtst-dev`, `libXi-dev`)
  - **Windows**: No additional dependencies
  - **macOS**: No additional dependencies

## Quick Start

### Using Nix (Recommended)

1. Enter the development environment:
```bash
nix-shell
```

2. Build the project:
```bash
cargo build --release
```

3. Run the auto clicker:
```bash
# Normal mode
cargo run --release

# Check function mode (detailed timing analysis)
cargo run --release check_function()
```

### Manual Build

1. Install Rust and Cargo if not already installed
2. Install system dependencies (Linux only):
```bash
# Ubuntu/Debian
sudo apt-get install libx11-dev libxtst-dev libxi-dev libxdo-dev

# Fedora/RHEL
sudo dnf install libX11-devel libXtst-devel libXi-devel xdotool-devel

# Arch Linux
sudo pacman -S libx11 libxtst libxi xdotool
```

3. Build and run:
```bash
cargo build --release

# Normal mode
cargo run --release

# Check function mode (detailed timing analysis)
cargo run --release check_function()
```

## Usage

### Normal Mode
1. **Start the application**: Run `cargo run --release`
2. **Prepare during countdown**: You have 3 seconds to:
   - Position your mouse cursor where you want clicks to occur
   - Focus on the target window/application
   - Get ready!
3. **Let it click**: The application will start clicking automatically with randomized intervals
4. **Stop the application**:
   - `Ctrl+C` when terminal is in focus
   - `Ctrl+Shift+Q` global hotkey (works even when other windows are focused)
   - Both work during countdown and clicking phases

### Check Function Mode
For detailed timing analysis and debugging, use:
```bash
nix-shell --run "cargo run check_function()"
```

This mode provides:
- **Detailed timing information** for each click interval
- **Human behavior analysis**: fatigue factors, pattern history, jitter coordinates
- **Actual vs expected timing** comparisons
- **Timing drift detection** and analysis
- **Click execution details**: hold duration, jitter applied, total click time
- **Performance statistics** including averages, min/max intervals
- **Real-time human simulation monitoring**

### Global Hotkey Support
The auto clicker supports **global hotkeys** that work even when the terminal is not in focus:

- **Ctrl+Shift+Q**: Stop the auto clicker from anywhere
- **Ctrl+C**: Traditional stop (only works when terminal is focused)

This is especially useful when:
- Clicking on other applications or games
- The terminal window is minimized or hidden
- You need emergency stop functionality
- Working with fullscreen applications

### Example Output

#### Normal Mode
```
🖱️  Auto Clicker Started!
📍 Clicking with human-like behavior patterns
⏱️  Adaptive intervals: 100ms - 700ms (+ human factors)
🧠 Features: fatigue simulation, pattern avoidance, micro-jitter
🛑 Stop: Ctrl+C (terminal focus) or Ctrl+Shift+Q (global)
💡 Tip: Use 'cargo run check_function()' for detailed timing info

⏰ Starting in 3 seconds...
💡 Use this time to:
   • Position your mouse cursor where you want clicks
   • Focus on the target window/application
   • Get ready!
⏰ Starting in 3...
⏰ Starting in 2...
⏰ Starting in 1...
🚀 Starting auto clicker now!
---
⏳ Next click in 342ms...
🖱️  Click #1 performed
⏳ Next click in 156ms...
🖱️  Click #2 performed
^C
🛑 Shutdown signal received. Stopping auto clicker...
---
✅ Auto Clicker stopped gracefully
📊 Total clicks performed: 2
👋 Goodbye!
```

#### Check Function Mode
```
🔍 Auto Clicker - CHECK FUNCTION MODE
📊 This mode shows detailed timing information
📍 Clicking with human-like behavior patterns
⏱️  Adaptive intervals: 100ms - 700ms (+ human factors)
🧠 Human simulation: fatigue, patterns, jitter, pauses
🔍 Detailed timing analysis will be displayed
🛑 Stop: Ctrl+C (terminal focus) or Ctrl+Shift+Q (global)

⏰ Starting in 3 seconds...
💡 Use this time to:
   • Position your mouse cursor where you want clicks
   • Focus on the target window/application
   • Get ready!
🚀 Starting auto clicker now!
---
🔍 [CHECK] Generated interval: 588ms (human-like)
🧠 [CHECK] Fatigue factor: 1.00x
📈 [CHECK] Pattern history: [588]
⏳ [CHECK] Waiting 588ms before next click...
✅ [CHECK] Click #1 performed in 49803μs
🎯 [CHECK] Jitter applied: (-1, -2) pixels
⏱️  [CHECK] Click hold time: 44873μs
⏱️  [CHECK] Actual wait time: 588ms (expected: 588ms)
---
🔍 [CHECK] Generated interval: 1411ms (human-like)
📊 [CHECK] Actual time since last click: 0ms
🧠 [CHECK] Fatigue factor: 1.00x
📈 [CHECK] Pattern history: [1411, 588]
⏳ [CHECK] Waiting 1411ms before next click...
✅ [CHECK] Click #2 performed in 40962μs
🎯 [CHECK] Jitter applied: (-1, 0) pixels
⏱️  [CHECK] Click hold time: 36896μs
⏱️  [CHECK] Actual wait time: 1412ms (expected: 1411ms)
⚠️  [CHECK] Timing drift: 1ms
---
^C
🛑 Shutdown signal received. Stopping auto clicker...
---
✅ Auto Clicker stopped gracefully
📊 Total clicks performed: 2

📈 DETAILED TIMING STATISTICS:
🔍 Total wait time: 498ms
📊 Average actual interval: 343.00ms
⏱️  Min interval: 343ms, Max interval: 343ms
⏳ Average wait per click: 249.00ms
🎯 Expected range: 100ms - 700ms
👋 Goodbye!
```

## Configuration

The application uses hardcoded values for simplicity:
- **Click interval range**: 100ms - 700ms
- **Mouse button**: Left button
- **Click location**: Current cursor position

To modify these values, edit the constants in `src/main.rs`.

## Development

### Project Structure
```
ac-rs/
├── Cargo.toml          # Project dependencies and metadata
├── shell.nix           # Nix development environment
├── src/
│   └── main.rs         # Main application code
└── README.md           # This file
```

### Dependencies
- **enigo**: Cross-platform input simulation
- **rand**: Random number generation for intervals
- **tokio**: Async runtime for timing and signal handling
- **ctrlc**: Cross-platform signal handling

### Building for Release
```bash
cargo build --release
```

The optimized binary will be available at `target/release/ac-rs`.

## Safety and Ethics

⚠️ **Important**: This tool is intended for legitimate automation purposes only. Please:
- Use responsibly and in accordance with applicable terms of service
- Do not use for cheating in games or violating software agreements
- Be mindful of the applications you're interacting with
- Test in safe environments before production use

## License

This project is provided as-is for educational and legitimate automation purposes.

## Troubleshooting

### Linux Issues
- **Permission denied**: Ensure your user has access to X11 display
- **Library not found**: Install X11 development packages
- **Wayland compatibility**: The application works best with X11; Wayland support may vary

### General Issues
- **High CPU usage**: This is normal due to the continuous clicking loop
- **Clicks not registering**: Ensure the target application has focus and accepts mouse input
- **Random intervals not working**: Check that the `rand` crate is properly installed

## Contributing

Feel free to submit issues and enhancement requests!
