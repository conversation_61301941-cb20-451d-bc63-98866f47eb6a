{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Rust toolchain
    rustc
    cargo
    rustfmt
    clippy
    
    # Development tools
    pkg-config
    
    # System libraries required for cross-platform mouse control
    # Linux dependencies
    xorg.libX11
    xorg.libXtst
    xorg.libXi
    xorg.libXrandr
    xorg.libXinerama

    # Additional libraries that might be needed
    libxkbcommon
    wayland

    # Required for enigo input simulation
    xdotool
    
    # Build tools
    gcc
    
    # Optional: useful development tools
    git
    vim
  ];

  # Environment variables
  shellHook = ''
    echo "🦀 Rust Auto Clicker Development Environment"
    echo "📦 Available tools:"
    echo "  - cargo build    : Build the project"
    echo "  - cargo run      : Run the auto clicker"
    echo "  - cargo test     : Run tests"
    echo "  - cargo clippy   : Run linter"
    echo "  - cargo fmt      : Format code"
    echo ""
    echo "🔧 System libraries for cross-platform support are available"
    echo "🌐 Global hotkey support enabled (Ctrl+Shift+Q)"
    echo "💡 Run 'cargo build' to compile the auto clicker"
    echo ""
    
    # Set up environment for X11 if available
    export DISPLAY=''${DISPLAY:-:0}
    
    # Ensure cargo is in PATH
    export PATH="$HOME/.cargo/bin:$PATH"
  '';

  # Additional environment variables for cross-platform compatibility
  LD_LIBRARY_PATH = pkgs.lib.makeLibraryPath [
    pkgs.xorg.libX11
    pkgs.xorg.libXtst
    pkgs.xorg.libXi
    pkgs.libxkbcommon
    pkgs.wayland
    pkgs.xdotool
  ];
}
